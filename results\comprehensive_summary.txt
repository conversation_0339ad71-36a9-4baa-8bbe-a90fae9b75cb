K-means Image Compression - Comprehensive Results
============================================================

Total Runtime: 90.43 seconds

Image           K     Iterations   Time (s)   Converged 
------------------------------------------------------------

# DOG (Shape: (275, 400, 3))
dog             3     13           0.19       True      
dog             6     29           0.74       True      
dog             12    42           1.94       True      
dog             24    50           4.67       False     
dog             48    38           6.85       True      

# FOOTBALL (Shape: (412, 620, 3))
football        3     18           0.59       True      
football        6     29           1.59       True      
football        12    50           5.41       False     
football        24    50           10.46      False     
football        48    50           20.37      False     

# PARROTS (Shape: (393, 600, 3))
parrots         3     23           0.85       True      
parrots         6     50           2.80       False     
parrots         12    50           5.10       False     
parrots         24    50           9.68       False     
parrots         48    50           18.81      False     


Deliverables Generated:
1. Reconstructed images for every K on all 3 images
2. Time in seconds for convergence for every K on each image
3. Number of iterations to convergence for every K on each image
4. Comparison plots showing original vs reconstructed images
