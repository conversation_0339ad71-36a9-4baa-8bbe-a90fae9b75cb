K-means Image Compression - Comprehensive Results
============================================================

Total Runtime: 265.80 seconds

Image           K     Iterations   Time (s)   Converged 
------------------------------------------------------------

# DOG (Shape: (275, 400, 3))
dog             3     13           0.16       True      
dog             6     29           0.61       True      
dog             12    42           1.69       True      
dog             24    58           4.46       True      
dog             48    38           5.81       True      

# FOOTBALL (Shape: (412, 620, 3))
football        3     18           0.56       True      
football        6     29           1.61       True      
football        12    67           6.62       True      
football        24    122          22.94      True      
football        48    204          69.60      True      

# PARROTS (Shape: (393, 600, 3))
parrots         3     23           0.62       True      
parrots         6     158          7.63       True      
parrots         12    134          12.59      True      
parrots         24    291          49.53      True      
parrots         48    260          81.02      True      


Deliverables Generated:
1. Reconstructed images for every K on all 3 images
2. Time in seconds for convergence for every K on each image
3. Number of iterations to convergence for every K on each image
4. Comparison plots showing original vs reconstructed images
