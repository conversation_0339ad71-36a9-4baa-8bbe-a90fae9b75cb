######################################################################
# Test Script for K-means Image Compression Implementation
# ISYE 6740 - Computational Data Analytics
# 
# This script tests the K-means implementation with a simple synthetic example
# and verifies the input/output format requirements.
######################################################################

import numpy as np
import matplotlib.pyplot as plt
from kmeans_image_compression import kmeans_image_compression, load_image_as_pixels, reconstruct_image
import os

def test_synthetic_data():
    """
    Test K-means with synthetic RGB data to verify correctness.
    """
    print("Testing K-means with synthetic RGB data...")
    
    # Create synthetic RGB data with 3 clear clusters
    np.random.seed(42)
    
    # Cluster 1: Red-ish pixels
    cluster1 = np.random.normal([200, 50, 50], [20, 20, 20], (100, 3))
    
    # Cluster 2: Green-ish pixels  
    cluster2 = np.random.normal([50, 200, 50], [20, 20, 20], (100, 3))
    
    # Cluster 3: Blue-ish pixels
    cluster3 = np.random.normal([50, 50, 200], [20, 20, 20], (100, 3))
    
    # Combine clusters
    pixels = np.vstack([cluster1, cluster2, cluster3])
    
    # Clip to valid RGB range
    pixels = np.clip(pixels, 0, 255)
    
    print(f"Synthetic data shape: {pixels.shape}")
    print(f"Pixel value range: [{pixels.min():.1f}, {pixels.max():.1f}]")
    
    # Run K-means
    k = 3
    class_assignments, centroids, iterations, converged = kmeans_image_compression(
        pixels, k, random_seed=42
    )
    
    # Verify output format
    print(f"\nOutput verification:")
    print(f"Class assignments shape: {class_assignments.shape}")
    print(f"Class assignments range: [{class_assignments.min()}, {class_assignments.max()}]")
    print(f"Centroids shape: {centroids.shape}")
    print(f"Centroids range: [{centroids.min():.1f}, {centroids.max():.1f}]")
    print(f"Iterations: {iterations}")
    print(f"Converged: {converged}")
    
    # Check requirements
    assert class_assignments.shape[0] == pixels.shape[0], "Class assignments should have same length as pixels"
    assert np.all((class_assignments >= 1) & (class_assignments <= k)), "Class assignments should be 1-based and in range [1, k]"
    assert centroids.shape == (k, 3), f"Centroids should be {k}x3 matrix"
    assert np.all((centroids >= 0) & (centroids <= 255)), "Centroids should be in range [0, 255]"
    
    print("✓ All format requirements satisfied!")
    
    # Print centroids
    print(f"\nFound centroids:")
    for i, centroid in enumerate(centroids):
        print(f"  Cluster {i+1}: RGB({centroid[0]:.1f}, {centroid[1]:.1f}, {centroid[2]:.1f})")
    
    return True


def test_image_loading():
    """
    Test image loading functionality with a small test image.
    """
    print("\nTesting image loading functionality...")
    
    # Create a small test image (10x10 pixels)
    test_image = np.zeros((10, 10, 3), dtype=np.uint8)
    
    # Create a simple pattern
    test_image[:5, :5] = [255, 0, 0]    # Red square
    test_image[:5, 5:] = [0, 255, 0]    # Green square  
    test_image[5:, :5] = [0, 0, 255]    # Blue square
    test_image[5:, 5:] = [255, 255, 0]  # Yellow square
    
    # Save test image
    from PIL import Image
    test_img_path = "test_image.png"
    Image.fromarray(test_image).save(test_img_path)
    
    try:
        # Test loading
        pixels, original_shape = load_image_as_pixels(test_img_path)
        
        print(f"Original shape: {original_shape}")
        print(f"Pixels shape: {pixels.shape}")
        print(f"Expected pixels: {10 * 10}")
        
        # Test reconstruction
        # Create dummy class assignments and centroids
        class_assignments = np.array([1, 2, 3, 4] * 25)  # 100 assignments
        centroids = np.array([[255, 0, 0], [0, 255, 0], [0, 0, 255], [255, 255, 0]])
        
        reconstructed = reconstruct_image(class_assignments, centroids, original_shape)
        
        print(f"Reconstructed shape: {reconstructed.shape}")
        print(f"Reconstructed dtype: {reconstructed.dtype}")
        
        assert pixels.shape[0] == 100, "Should have 100 pixels"
        assert pixels.shape[1] == 3, "Should have 3 color channels"
        assert original_shape == (10, 10, 3), "Original shape should be preserved"
        assert reconstructed.shape == original_shape, "Reconstructed shape should match original"
        
        print("✓ Image loading and reconstruction working correctly!")
        
    finally:
        # Clean up test image
        if os.path.exists(test_img_path):
            os.remove(test_img_path)
    
    return True


def test_convergence():
    """
    Test convergence behavior with different scenarios.
    """
    print("\nTesting convergence behavior...")
    
    # Test 1: Well-separated clusters (should converge quickly)
    np.random.seed(123)
    cluster1 = np.random.normal([50, 50, 50], [10, 10, 10], (50, 3))
    cluster2 = np.random.normal([200, 200, 200], [10, 10, 10], (50, 3))
    pixels = np.vstack([cluster1, cluster2])
    pixels = np.clip(pixels, 0, 255)
    
    class_assignments, centroids, iterations, converged = kmeans_image_compression(
        pixels, 2, max_iterations=50, random_seed=123
    )
    
    print(f"Well-separated clusters: {iterations} iterations, converged: {converged}")
    
    # Test 2: Single cluster (should converge in 1 iteration)
    pixels_single = np.random.normal([128, 128, 128], [20, 20, 20], (100, 3))
    pixels_single = np.clip(pixels_single, 0, 255)
    
    class_assignments, centroids, iterations, converged = kmeans_image_compression(
        pixels_single, 1, max_iterations=50, random_seed=123
    )
    
    print(f"Single cluster: {iterations} iterations, converged: {converged}")
    
    print("✓ Convergence behavior working correctly!")
    
    return True


if __name__ == "__main__":
    print("K-means Image Compression - Test Suite")
    print("=" * 50)
    
    try:
        # Run all tests
        test_synthetic_data()
        test_image_loading()
        test_convergence()
        
        print("\n" + "=" * 50)
        print("✓ ALL TESTS PASSED!")
        print("The K-means implementation is ready for use.")
        print("\nTo run the full experiments, execute:")
        print("python run_image_compression_experiments.py")
        
    except Exception as e:
        print(f"\n❌ TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
