######################################################################
# K-means Image Compression Implementation
# ISYE 6740 - Computational Data Analytics
# 
# This implementation provides K-means clustering for image compression
# following the assignment specifications.
######################################################################

import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import time
import os

def kmeans_image_compression(pixels, k, max_iterations=100, tolerance=1e-4, random_seed=None):
    """
    Highly optimized, vectorized K-means clustering algorithm for image compression.

    Input:
    - pixels: numpy array where each row contains one data point (pixel).
              For image dataset, it contains 3 columns (R, G, B) with values [0, 255].
    - k: the number of desired clusters.
    - max_iterations: maximum number of iterations (default: 100)
    - tolerance: convergence tolerance (default: 1e-4)
    - random_seed: random seed for reproducibility (default: None)

    Output:
    - class: cluster assignment of each data point in pixels.
             Assignment should be 1, 2, 3, etc. Column vector with size(pixels, 0) elements.
    - centroid: location of k centroids. Matrix with k rows and 3 columns.
                Values in range [0, 255], possibly floating point.
    - iterations: number of iterations until convergence
    - converged: whether the algorithm converged
    """

    if random_seed is not None:
        np.random.seed(random_seed)

    n_samples, n_features = pixels.shape
    pixels_float = pixels.astype(np.float64)

    # Initialize centroids randomly from the data points
    centroid_indices = np.random.choice(n_samples, k, replace=False)
    centroids = pixels_float[centroid_indices].copy()

    # Pre-allocate arrays for efficiency
    prev_labels = np.zeros(n_samples, dtype=np.int32)

    for iteration in range(max_iterations):
        # Highly vectorized distance calculation using broadcasting
        # Shape: (n_samples, k)
        distances_squared = np.sum((pixels_float[:, np.newaxis, :] - centroids[np.newaxis, :, :]) ** 2, axis=2)

        # Find closest centroid for each pixel
        labels = np.argmin(distances_squared, axis=1)

        # Check for convergence (no label changes)
        if iteration > 0 and np.array_equal(labels, prev_labels):
            converged = True
            break

        prev_labels = labels.copy()

        # Vectorized centroid update using advanced indexing
        # This is much faster than looping
        for i in range(k):
            mask = (labels == i)
            if np.any(mask):
                centroids[i] = np.mean(pixels_float[mask], axis=0)

        # Early convergence check based on centroid movement
        if iteration > 0:
            centroid_shift = np.sum((centroids - prev_centroids) ** 2)
            if centroid_shift < tolerance:
                converged = True
                break

        prev_centroids = centroids.copy()
    else:
        converged = False

    # Convert labels to 1-based indexing as required
    class_assignments = labels + 1

    # Ensure centroids are in valid range [0, 255]
    centroids = np.clip(centroids, 0, 255)

    return class_assignments, centroids, iteration + 1, converged


def load_image_as_pixels(image_path):
    """
    Load an image and convert it to the required pixel format.
    
    Input:
    - image_path: path to the image file
    
    Output:
    - pixels: numpy array where each row is a pixel with RGB values [0, 255]
    - original_shape: original image shape (height, width, channels) for reconstruction
    """
    
    # Load image using PIL
    img = Image.open(image_path)
    
    # Convert to RGB if not already
    if img.mode != 'RGB':
        img = img.convert('RGB')
    
    # Convert to numpy array
    img_array = np.array(img)
    original_shape = img_array.shape
    
    # Reshape to pixels format (each row is a pixel)
    pixels = img_array.reshape(-1, 3)
    
    return pixels, original_shape


def reconstruct_image(class_assignments, centroids, original_shape):
    """
    Reconstruct the compressed image from cluster assignments and centroids.
    
    Input:
    - class_assignments: cluster assignment for each pixel (1-based indexing)
    - centroids: k centroids with RGB values
    - original_shape: original image shape (height, width, channels)
    
    Output:
    - reconstructed_image: numpy array representing the reconstructed image
    """
    
    # Convert class assignments back to 0-based indexing
    labels = class_assignments - 1
    
    # Create reconstructed pixel array
    reconstructed_pixels = centroids[labels]
    
    # Reshape back to original image shape
    reconstructed_image = reconstructed_pixels.reshape(original_shape)
    
    # Ensure values are in valid range and convert to uint8
    reconstructed_image = np.clip(reconstructed_image, 0, 255).astype(np.uint8)
    
    return reconstructed_image


def run_kmeans_experiment(image_path, k_values, num_trials=5):
    """
    Run K-means experiment on an image with multiple k values and trials.
    
    Input:
    - image_path: path to the image file
    - k_values: list of k values to test
    - num_trials: number of trials per k value to find best result
    
    Output:
    - results: dictionary containing results for each k value
    """
    
    print(f"Loading image: {image_path}")
    pixels, original_shape = load_image_as_pixels(image_path)
    print(f"Image shape: {original_shape}, Total pixels: {len(pixels)}")
    
    results = {}
    
    for k in k_values:
        print(f"\nRunning K-means with k={k}")
        
        best_result = None
        best_inertia = float('inf')
        
        for trial in range(num_trials):
            print(f"  Trial {trial + 1}/{num_trials}")
            
            # Run K-means
            start_time = time.time()
            class_assignments, centroids, iterations, converged = kmeans_image_compression(
                pixels, k, random_seed=trial
            )
            end_time = time.time()
            
            # Calculate inertia (sum of squared distances to centroids)
            labels = class_assignments - 1
            inertia = 0
            for i in range(k):
                cluster_mask = (labels == i)
                if np.sum(cluster_mask) > 0:
                    cluster_pixels = pixels[cluster_mask]
                    centroid = centroids[i]
                    inertia += np.sum((cluster_pixels - centroid) ** 2)
            
            # Keep track of best result (lowest inertia)
            if inertia < best_inertia:
                best_inertia = inertia
                best_result = {
                    'class_assignments': class_assignments,
                    'centroids': centroids,
                    'iterations': iterations,
                    'converged': converged,
                    'time_seconds': end_time - start_time,
                    'inertia': inertia,
                    'trial': trial
                }
        
        # Reconstruct image with best result
        reconstructed_image = reconstruct_image(
            best_result['class_assignments'], 
            best_result['centroids'], 
            original_shape
        )
        
        best_result['reconstructed_image'] = reconstructed_image
        results[k] = best_result
        
        print(f"  Best result: Trial {best_result['trial']}, "
              f"Iterations: {best_result['iterations']}, "
              f"Time: {best_result['time_seconds']:.2f}s, "
              f"Converged: {best_result['converged']}")
    
    return results, original_shape


def save_results(results, original_image_path, output_dir="results"):
    """
    Save the reconstructed images and create a summary report.
    """
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Get base filename
    base_name = os.path.splitext(os.path.basename(original_image_path))[0]
    
    # Save reconstructed images
    for k, result in results.items():
        output_path = os.path.join(output_dir, f"{base_name}_k{k}_reconstructed.png")
        Image.fromarray(result['reconstructed_image']).save(output_path)
        print(f"Saved: {output_path}")
    
    # Create summary report
    report_path = os.path.join(output_dir, f"{base_name}_summary.txt")
    with open(report_path, 'w') as f:
        f.write(f"K-means Image Compression Results for {base_name}\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("K\tIterations\tTime (s)\tConverged\n")
        f.write("-" * 40 + "\n")
        
        for k in sorted(results.keys()):
            result = results[k]
            f.write(f"{k}\t{result['iterations']}\t\t{result['time_seconds']:.2f}\t\t{result['converged']}\n")
    
    print(f"Summary saved: {report_path}")


if __name__ == "__main__":
    # Test with a simple example
    print("K-means Image Compression Implementation")
    print("This module provides functions for image compression using K-means clustering.")
    print("Use the main execution script to run experiments on the assignment images.")
