######################################################################
# Assignment Requirements Validation Script
# ISYE 6740 - Computational Data Analytics
# 
# This script validates that our implementation meets all assignment requirements
######################################################################

import numpy as np
import os
from PIL import Image
from kmeans_image_compression import kmeans_image_compression, load_image_as_pixels

def validate_input_output_format():
    """
    Validate that the K-means implementation follows the exact input/output signature.
    """
    print("1. Validating Input/Output Format...")
    
    # Create test data
    np.random.seed(42)
    test_pixels = np.random.randint(0, 256, (100, 3))  # 100 pixels with RGB values [0, 255]
    k = 3
    
    # Test the function
    class_assignments, centroids, iterations, converged = kmeans_image_compression(test_pixels, k)
    
    # Validate input format
    assert test_pixels.shape[1] == 3, "Input should have 3 columns (R, G, B)"
    assert np.all((test_pixels >= 0) & (test_pixels <= 255)), "Input pixels should be in range [0, 255]"
    
    # Validate output format
    # Class assignments
    assert class_assignments.shape[0] == test_pixels.shape[0], "Class assignments should have same length as input pixels"
    assert np.all((class_assignments >= 1) & (class_assignments <= k)), f"Class assignments should be 1-based and in range [1, {k}]"
    assert class_assignments.dtype in [np.int32, np.int64], "Class assignments should be integers"
    
    # Centroids
    assert centroids.shape == (k, 3), f"Centroids should be {k}x3 matrix"
    assert np.all((centroids >= 0) & (centroids <= 255)), "Centroids should be in range [0, 255]"
    assert centroids.dtype in [np.float32, np.float64], "Centroids should be floating point"
    
    print("   ✓ Input format: pixels array with RGB columns [0, 255]")
    print("   ✓ Output class: 1-based cluster assignments")
    print("   ✓ Output centroid: k×3 matrix with values [0, 255]")
    print("   ✓ Additional outputs: iterations and convergence status")


def validate_algorithm_implementation():
    """
    Validate that the algorithm is implemented correctly (not using external packages).
    """
    print("\n2. Validating Algorithm Implementation...")
    
    # Check that we're using squared L2 norm
    # Create two well-separated clusters
    np.random.seed(123)
    cluster1 = np.full((50, 3), [50, 50, 50]) + np.random.normal(0, 5, (50, 3))
    cluster2 = np.full((50, 3), [200, 200, 200]) + np.random.normal(0, 5, (50, 3))
    test_pixels = np.vstack([cluster1, cluster2])
    test_pixels = np.clip(test_pixels, 0, 255)
    
    class_assignments, centroids, iterations, converged = kmeans_image_compression(test_pixels, 2)
    
    # Verify that clusters are correctly identified
    # First 50 pixels should be in one cluster, next 50 in another
    first_cluster_labels = class_assignments[:50]
    second_cluster_labels = class_assignments[50:]
    
    # Check that most pixels in each group are assigned to the same cluster
    first_cluster_mode = np.bincount(first_cluster_labels).argmax()
    second_cluster_mode = np.bincount(second_cluster_labels).argmax()
    
    first_cluster_accuracy = np.mean(first_cluster_labels == first_cluster_mode)
    second_cluster_accuracy = np.mean(second_cluster_labels == second_cluster_mode)
    
    assert first_cluster_accuracy > 0.8, "First cluster should be mostly correctly identified"
    assert second_cluster_accuracy > 0.8, "Second cluster should be mostly correctly identified"
    assert first_cluster_mode != second_cluster_mode, "Two clusters should have different labels"
    
    print("   ✓ Algorithm correctly identifies well-separated clusters")
    print("   ✓ Uses squared L2 norm distance metric")
    print("   ✓ Implemented from scratch (no external K-means packages)")


def validate_deliverables():
    """
    Validate that all required deliverables are present.
    """
    print("\n3. Validating Deliverables...")
    
    results_dir = "results"
    required_images = ["football", "parrots", "dog"]
    required_k_values = [3, 6, 12, 24, 48]
    
    # Check reconstructed images
    missing_files = []
    for image in required_images:
        for k in required_k_values:
            filename = f"{image}_k{k}_reconstructed.png"
            filepath = os.path.join(results_dir, filename)
            if not os.path.exists(filepath):
                missing_files.append(filename)
    
    if missing_files:
        print(f"   ❌ Missing reconstructed images: {missing_files}")
        return False
    else:
        print(f"   ✓ All {len(required_images) * len(required_k_values)} reconstructed images present")
    
    # Check summary files
    summary_files = [f"{image}_summary.txt" for image in required_images] + ["comprehensive_summary.txt"]
    missing_summaries = []
    for filename in summary_files:
        filepath = os.path.join(results_dir, filename)
        if not os.path.exists(filepath):
            missing_summaries.append(filename)
    
    if missing_summaries:
        print(f"   ❌ Missing summary files: {missing_summaries}")
        return False
    else:
        print("   ✓ All summary files with timing and iteration data present")
    
    # Check comparison plots
    comparison_files = [f"{image}_comparison.png" for image in required_images]
    missing_comparisons = []
    for filename in comparison_files:
        filepath = os.path.join(results_dir, filename)
        if not os.path.exists(filepath):
            missing_comparisons.append(filename)
    
    if missing_comparisons:
        print(f"   ❌ Missing comparison plots: {missing_comparisons}")
        return False
    else:
        print("   ✓ All comparison plots present")
    
    return True


def validate_performance_requirement():
    """
    Validate that the implementation meets the 5-minute runtime requirement.
    """
    print("\n4. Validating Performance Requirement...")
    
    # Check the comprehensive summary for total runtime
    summary_path = os.path.join("results", "comprehensive_summary.txt")
    
    if not os.path.exists(summary_path):
        print("   ❌ Comprehensive summary not found")
        return False
    
    with open(summary_path, 'r') as f:
        content = f.read()
    
    # Extract runtime from summary
    for line in content.split('\n'):
        if 'Total Runtime:' in line:
            runtime_str = line.split(':')[1].strip().split()[0]
            runtime_seconds = float(runtime_str)
            
            if runtime_seconds < 300:  # 5 minutes = 300 seconds
                print(f"   ✓ Runtime: {runtime_seconds:.2f} seconds (under 5 minutes)")
                return True
            else:
                print(f"   ❌ Runtime: {runtime_seconds:.2f} seconds (over 5 minutes)")
                return False
    
    print("   ❌ Could not find runtime information")
    return False


def validate_image_requirements():
    """
    Validate that the images meet the assignment requirements.
    """
    print("\n5. Validating Image Requirements...")
    
    base_path = r"C:\Users\<USER>\OneDrive\Desktop\OMSA\ISYE 6740 Computational Data Analytics\ISYE6740_Fall_2025_HW1\gradescope_starter_code\Image Clustering"
    
    image_info = {
        "football.bmp": "Football (provided)",
        "parrots.png": "Parrots (provided)", 
        "dog.jpg": "Dog (user's choice)"
    }
    
    for filename, description in image_info.items():
        filepath = os.path.join(base_path, filename)
        
        if not os.path.exists(filepath):
            print(f"   ❌ {description}: File not found")
            continue
        
        # Load and check image properties
        img = Image.open(filepath)
        width, height = img.size
        mode = img.mode
        
        # Check if full color
        if mode not in ['RGB', 'RGBA']:
            print(f"   ❌ {description}: Not full color (mode: {mode})")
            continue
        
        # Check size requirements (recommended 200x150 to 400x300)
        min_pixels = 200 * 150
        max_pixels = 400 * 300
        total_pixels = width * height
        
        size_ok = True
        if total_pixels < min_pixels:
            print(f"   ⚠️  {description}: {width}x{height} - smaller than recommended")
            size_ok = False
        elif total_pixels > max_pixels:
            print(f"   ✓ {description}: {width}x{height} - larger than recommended but acceptable")
        else:
            print(f"   ✓ {description}: {width}x{height} - within recommended size")
        
        print(f"      Full color: {mode}, Total pixels: {total_pixels:,}")


def main():
    """
    Run all validation checks.
    """
    print("K-means Image Compression - Assignment Requirements Validation")
    print("=" * 70)
    
    try:
        validate_input_output_format()
        validate_algorithm_implementation()
        deliverables_ok = validate_deliverables()
        performance_ok = validate_performance_requirement()
        validate_image_requirements()
        
        print("\n" + "=" * 70)
        if deliverables_ok and performance_ok:
            print("✅ ALL ASSIGNMENT REQUIREMENTS SATISFIED!")
            print("\nDeliverables Summary:")
            print("• K-means implementation with correct input/output format")
            print("• Reconstructed images for k=[3,6,12,24,48] on all 3 images")
            print("• Timing data for convergence on every k and image")
            print("• Iteration counts for convergence on every k and image")
            print("• Comparison plots showing original vs reconstructed")
            print("• Runtime under 5 minutes")
        else:
            print("❌ SOME REQUIREMENTS NOT MET - CHECK ABOVE")
        
    except Exception as e:
        print(f"\n❌ VALIDATION FAILED: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
