K-means Distance Metrics Comparison Analysis
============================================================

Total Runtime: 333.88 seconds

PERFORMANCE COMPARISON
------------------------------

Convergence Comparison:

DOG:
K     L2 Conv    L2 Iter    L1 Conv    L1 Iter    Time Diff   
------------------------------------------------------------
3     <USER>        <GROUP>         Yes        12         +0.01s
6     Yes        29         Yes        28         +0.09s
12    Yes        42         Yes        19         -0.87s
24    Yes        58         Yes        13         -3.55s
48    Yes        38         Yes        14         -3.89s

FOOTBALL:
K     L2 Conv    L2 Iter    L1 Conv    L1 Iter    Time Diff   
------------------------------------------------------------
3     <USER>        <GROUP>         Yes        8          -0.21s
6     Yes        29         Yes        11         -0.92s
12    Yes        67         Yes        39         -2.03s
24    Yes        122        Yes        21         -19.43s
48    Yes        204        Yes        28         -60.69s

PARROTS:
K     L2 Conv    L2 Iter    L1 Conv    L1 Iter    Time Diff   
------------------------------------------------------------
3     <USER>        <GROUP>         Yes        11         -0.21s
6     Yes        158        Yes        19         -6.78s
12    Yes        134        Yes        34         -9.32s
24    Yes        291        Yes        35         -44.53s
48    Yes        260        Yes        31         -74.05s


OBSERVATIONS:
---------------
1. L2 norm (squared Euclidean distance) vs L1 norm (Manhattan distance)
2. L1 norm uses median for centroid updates, L2 norm uses mean
3. L1 norm is more robust to outliers due to median-based centroids
4. L2 norm typically converges faster due to smoother optimization landscape
5. Visual differences may be subtle but can affect color clustering
