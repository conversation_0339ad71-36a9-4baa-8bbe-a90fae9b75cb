######################################################################
# Optimized K-means Experiment for Dog Image
# ISYE 6740 - Computational Data Analytics
# 
# This script runs K-means on the dog image with optimizations for speed.
######################################################################

import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import os
import sys
from kmeans_image_compression import run_kmeans_experiment, save_results, load_image_as_pixels, kmeans_image_compression, reconstruct_image
import time

def run_optimized_dog_experiment():
    """
    Run K-means experiment on dog image with optimizations for speed.
    """
    
    # Define image path
    base_path = r"C:\Users\<USER>\OneDrive\Desktop\OMSA\ISYE 6740 Computational Data Analytics\ISYE6740_Fall_2025_HW1\gradescope_starter_code\Image Clustering"
    image_path = os.path.join(base_path, "dog.jpg")
    
    # Define k values to test
    k_values = [3, 6, 12, 24, 48]
    
    # Reduced number of trials for speed
    num_trials = 3
    
    print("Starting Optimized K-means Experiment for Dog Image")
    print("=" * 60)
    print(f"K values: {k_values}")
    print(f"Number of trials per k: {num_trials}")
    
    # Check if image file exists
    if not os.path.exists(image_path):
        print(f"ERROR: Image file not found: {image_path}")
        sys.exit(1)
    else:
        print(f"✓ Found dog image: {image_path}")
    
    print("\n" + "=" * 60)
    
    # Load image
    print("Loading dog image...")
    pixels, original_shape = load_image_as_pixels(image_path)
    print(f"Image shape: {original_shape}, Total pixels: {len(pixels)}")
    
    # Create output directory
    output_dir = "results"
    os.makedirs(output_dir, exist_ok=True)
    
    # Run experiments
    results = {}
    
    for k in k_values:
        print(f"\nRunning K-means with k={k}")
        
        best_result = None
        best_inertia = float('inf')
        
        for trial in range(num_trials):
            print(f"  Trial {trial + 1}/{num_trials}")
            
            # Run K-means
            start_time = time.time()
            class_assignments, centroids, iterations, converged = kmeans_image_compression(
                pixels, k, max_iterations=50, random_seed=trial  # Reduced max iterations
            )
            end_time = time.time()
            
            # Calculate inertia (sum of squared distances to centroids)
            labels = class_assignments - 1
            inertia = 0
            for i in range(k):
                cluster_mask = (labels == i)
                if np.sum(cluster_mask) > 0:
                    cluster_pixels = pixels[cluster_mask]
                    centroid = centroids[i]
                    inertia += np.sum((cluster_pixels - centroid) ** 2)
            
            # Keep track of best result (lowest inertia)
            if inertia < best_inertia:
                best_inertia = inertia
                best_result = {
                    'class_assignments': class_assignments,
                    'centroids': centroids,
                    'iterations': iterations,
                    'converged': converged,
                    'time_seconds': end_time - start_time,
                    'inertia': inertia,
                    'trial': trial
                }
        
        # Reconstruct image with best result
        reconstructed_image = reconstruct_image(
            best_result['class_assignments'], 
            best_result['centroids'], 
            original_shape
        )
        
        best_result['reconstructed_image'] = reconstructed_image
        results[k] = best_result
        
        print(f"  Best result: Trial {best_result['trial']}, "
              f"Iterations: {best_result['iterations']}, "
              f"Time: {best_result['time_seconds']:.2f}s, "
              f"Converged: {best_result['converged']}")
        
        # Save individual result immediately
        output_path = os.path.join(output_dir, f"dog_k{k}_reconstructed.png")
        Image.fromarray(reconstructed_image).save(output_path)
        print(f"  Saved: {output_path}")
    
    # Save summary
    summary_path = os.path.join(output_dir, "dog_summary.txt")
    with open(summary_path, 'w') as f:
        f.write("K-means Image Compression Results for dog\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("K\tIterations\tTime (s)\tConverged\n")
        f.write("-" * 40 + "\n")
        
        for k in sorted(results.keys()):
            result = results[k]
            f.write(f"{k}\t{result['iterations']}\t\t{result['time_seconds']:.2f}\t\t{result['converged']}\n")
    
    print(f"\nSummary saved: {summary_path}")
    
    # Print results summary
    print("\n" + "=" * 60)
    print("DOG IMAGE RESULTS SUMMARY")
    print("=" * 60)
    print(f"Image Shape: {original_shape}")
    print("-" * 50)
    print(f"{'K':<5} {'Iterations':<12} {'Time (s)':<10} {'Converged':<10}")
    print("-" * 50)
    
    for k in sorted(results.keys()):
        result = results[k]
        print(f"{k:<5} {result['iterations']:<12} {result['time_seconds']:<10.2f} {str(result['converged']):<10}")
    
    print(f"\n{'='*20} EXPERIMENT COMPLETE {'='*20}")
    print(f"All results saved to: {os.path.abspath(output_dir)}")
    
    return results, original_shape


def create_dog_comparison_plot(results, original_shape, output_dir="results"):
    """
    Create comparison plot for dog image results.
    """
    
    # Load original image for comparison
    base_path = r"C:\Users\<USER>\OneDrive\Desktop\OMSA\ISYE 6740 Computational Data Analytics\ISYE6740_Fall_2025_HW1\gradescope_starter_code\Image Clustering"
    image_path = os.path.join(base_path, "dog.jpg")
    original_pixels, _ = load_image_as_pixels(image_path)
    original_image = original_pixels.reshape(original_shape)
    
    # Create subplot figure
    k_values = sorted(results.keys())
    n_plots = len(k_values) + 1  # +1 for original
    
    # Calculate subplot layout
    n_cols = 3
    n_rows = (n_plots + n_cols - 1) // n_cols
    
    fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5 * n_rows))
    if n_rows == 1:
        axes = axes.reshape(1, -1)
    
    # Plot original image
    axes[0, 0].imshow(original_image)
    axes[0, 0].set_title('Original Image')
    axes[0, 0].axis('off')
    
    # Plot reconstructed images
    plot_idx = 1
    for k in k_values:
        row = plot_idx // n_cols
        col = plot_idx % n_cols
        
        axes[row, col].imshow(results[k]['reconstructed_image'])
        axes[row, col].set_title(f'K={k} ({results[k]["iterations"]} iter, {results[k]["time_seconds"]:.2f}s)')
        axes[row, col].axis('off')
        
        plot_idx += 1
    
    # Hide unused subplots
    for i in range(plot_idx, n_rows * n_cols):
        row = i // n_cols
        col = i % n_cols
        axes[row, col].axis('off')
    
    plt.tight_layout()
    
    # Save comparison plot
    comparison_path = os.path.join(output_dir, "dog_comparison.png")
    plt.savefig(comparison_path, dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"Comparison plot saved: {comparison_path}")


if __name__ == "__main__":
    try:
        results, original_shape = run_optimized_dog_experiment()
        create_dog_comparison_plot(results, original_shape)
        
        print("\n✓ Dog image experiment completed successfully!")
        
    except Exception as e:
        print(f"\n❌ EXPERIMENT FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
