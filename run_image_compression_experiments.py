######################################################################
# Main Execution Script for K-means Image Compression Experiments
# ISYE 6740 - Computational Data Analytics
# 
# This script runs K-means clustering experiments on the three specified images
# with k values [3, 6, 12, 24, 48] and generates all required deliverables.
######################################################################

import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import os
import sys
from kmeans_image_compression import run_kmeans_experiment, save_results, load_image_as_pixels

def create_comparison_plots(results_dict, output_dir="results"):
    """
    Create comparison plots showing original vs reconstructed images for all k values.
    """
    
    for image_name, (results, original_shape) in results_dict.items():
        # Load original image for comparison
        original_image_path = image_paths[image_name]
        original_pixels, _ = load_image_as_pixels(original_image_path)
        original_image = original_pixels.reshape(original_shape)
        
        # Create subplot figure
        k_values = sorted(results.keys())
        n_plots = len(k_values) + 1  # +1 for original
        
        # Calculate subplot layout
        n_cols = 3
        n_rows = (n_plots + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5 * n_rows))
        if n_rows == 1:
            axes = axes.reshape(1, -1)
        
        # Plot original image
        axes[0, 0].imshow(original_image)
        axes[0, 0].set_title('Original Image')
        axes[0, 0].axis('off')
        
        # Plot reconstructed images
        plot_idx = 1
        for k in k_values:
            row = plot_idx // n_cols
            col = plot_idx % n_cols
            
            axes[row, col].imshow(results[k]['reconstructed_image'])
            axes[row, col].set_title(f'K={k} ({results[k]["iterations"]} iter, {results[k]["time_seconds"]:.2f}s)')
            axes[row, col].axis('off')
            
            plot_idx += 1
        
        # Hide unused subplots
        for i in range(plot_idx, n_rows * n_cols):
            row = i // n_cols
            col = i % n_cols
            axes[row, col].axis('off')
        
        plt.tight_layout()
        
        # Save comparison plot
        comparison_path = os.path.join(output_dir, f"{image_name}_comparison.png")
        plt.savefig(comparison_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"Comparison plot saved: {comparison_path}")


def create_summary_table(results_dict, output_dir="results"):
    """
    Create a comprehensive summary table with all timing and iteration data.
    """
    
    summary_path = os.path.join(output_dir, "experiment_summary.txt")
    
    with open(summary_path, 'w') as f:
        f.write("K-means Image Compression Experiment Summary\n")
        f.write("=" * 60 + "\n\n")
        
        # Table header
        f.write(f"{'Image':<15} {'K':<5} {'Iterations':<12} {'Time (s)':<10} {'Converged':<10}\n")
        f.write("-" * 60 + "\n")
        
        # Data rows
        for image_name in sorted(results_dict.keys()):
            results, _ = results_dict[image_name]
            
            for k in sorted(results.keys()):
                result = results[k]
                f.write(f"{image_name:<15} {k:<5} {result['iterations']:<12} "
                       f"{result['time_seconds']:<10.2f} {str(result['converged']):<10}\n")
            
            f.write("\n")  # Blank line between images
    
    print(f"Summary table saved: {summary_path}")


def print_results_summary(results_dict):
    """
    Print a formatted summary of results to console.
    """
    
    print("\n" + "=" * 80)
    print("EXPERIMENT RESULTS SUMMARY")
    print("=" * 80)
    
    for image_name, (results, original_shape) in results_dict.items():
        print(f"\n{image_name.upper()} (Shape: {original_shape})")
        print("-" * 50)
        print(f"{'K':<5} {'Iterations':<12} {'Time (s)':<10} {'Converged':<10}")
        print("-" * 50)
        
        for k in sorted(results.keys()):
            result = results[k]
            print(f"{k:<5} {result['iterations']:<12} {result['time_seconds']:<10.2f} {str(result['converged']):<10}")


if __name__ == "__main__":
    # Define image paths
    base_path = r"C:\Users\<USER>\OneDrive\Desktop\OMSA\ISYE 6740 Computational Data Analytics\ISYE6740_Fall_2025_HW1\gradescope_starter_code\Image Clustering"
    
    image_paths = {
        "football": os.path.join(base_path, "football.bmp"),
        "parrots": os.path.join(base_path, "parrots.png"),
        "dog": os.path.join(base_path, "dog.jpg")
    }
    
    # Define k values to test
    k_values = [3, 6, 12, 24, 48]
    
    # Number of trials per k value (to find best seed)
    num_trials = 5
    
    print("Starting K-means Image Compression Experiments")
    print("=" * 60)
    print(f"K values: {k_values}")
    print(f"Number of trials per k: {num_trials}")
    print(f"Images to process: {list(image_paths.keys())}")
    
    # Check if image files exist
    for name, path in image_paths.items():
        if not os.path.exists(path):
            print(f"ERROR: Image file not found: {path}")
            sys.exit(1)
        else:
            print(f"✓ Found {name}: {path}")
    
    print("\n" + "=" * 60)
    
    # Create output directory
    output_dir = "results"
    os.makedirs(output_dir, exist_ok=True)
    
    # Run experiments on all images
    results_dict = {}
    
    for image_name, image_path in image_paths.items():
        print(f"\n{'='*20} PROCESSING {image_name.upper()} {'='*20}")
        
        try:
            results, original_shape = run_kmeans_experiment(image_path, k_values, num_trials)
            results_dict[image_name] = (results, original_shape)
            
            # Save individual results
            save_results(results, image_path, output_dir)
            
        except Exception as e:
            print(f"ERROR processing {image_name}: {str(e)}")
            continue
    
    # Create comparison plots
    print(f"\n{'='*20} CREATING COMPARISON PLOTS {'='*20}")
    create_comparison_plots(results_dict, output_dir)
    
    # Create summary table
    print(f"\n{'='*20} CREATING SUMMARY TABLE {'='*20}")
    create_summary_table(results_dict, output_dir)
    
    # Print results summary
    print_results_summary(results_dict)
    
    print(f"\n{'='*20} EXPERIMENT COMPLETE {'='*20}")
    print(f"All results saved to: {os.path.abspath(output_dir)}")
    print("\nDeliverables generated:")
    print("1. Reconstructed images for every K on all 3 images")
    print("2. Time in seconds for convergence for every K on each image")
    print("3. Number of iterations to convergence for every K on each image")
    print("4. Comparison plots showing original vs reconstructed images")
    print("5. Summary table with all timing and iteration data")
